import React, { useState, useEffect } from 'react';
import { usePrivy } from '@privy-io/react-auth';
import { FaUser, FaUsers, FaSpinner } from 'react-icons/fa';
import { getRewardsData, ReferralLevel, ReferralUser } from '../api/rewards_api';

// Interfaces are now imported from rewards_api

const ReferralTree = () => {
  const { user } = usePrivy();
  const [referralLevels, setReferralLevels] = useState<ReferralLevel[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Load referral tree data
  useEffect(() => {
    if (user?.id) {
      loadReferralData();
    }
  }, [user]);

  // Simplified approach - no complex tree building needed

  const loadReferralData = async () => {
    if (!user?.id) return;

    setIsLoading(true);
    try {
      const response = await getRewardsData(user.id);
      if (response.success && response.data) {
        setReferralLevels(response.data.referralLevels || []);
      } else {
        // Fallback to mock data for demonstration
        const mockLevels: ReferralLevel[] = [
          { level: 1, commission: 40, count: 3, earnings: 450.25, referrals: [] },
          { level: 2, commission: 15, count: 4, earnings: 125.80, referrals: [] },
          { level: 3, commission: 10, count: 2, earnings: 67.40, referrals: [] },
          { level: 4, commission: 5, count: 0, earnings: 0, referrals: [] },
          { level: 5, commission: 2, count: 0, earnings: 0, referrals: [] },
        ];
        setReferralLevels(mockLevels);
      }
    } catch (error) {
      console.error('Error loading referral data:', error);
      // Fallback to empty levels
      const emptyLevels: ReferralLevel[] = [
        { level: 1, commission: 40, count: 0, earnings: 0, referrals: [] },
        { level: 2, commission: 15, count: 0, earnings: 0, referrals: [] },
        { level: 3, commission: 10, count: 0, earnings: 0, referrals: [] },
        { level: 4, commission: 5, count: 0, earnings: 0, referrals: [] },
        { level: 5, commission: 2, count: 0, earnings: 0, referrals: [] },
      ];
      setReferralLevels(emptyLevels);
    } finally {
      setIsLoading(false);
    }
  };

  const getTotalEarnings = () => {
    return referralLevels.reduce((total, level) => total + level.earnings, 0);
  };

  const getTotalReferrals = () => {
    return referralLevels.reduce((total, level) => total + level.count, 0);
  };

  // Simplified approach - no complex tree component needed

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <FaSpinner className="animate-spin text-[#7FFFD4] text-3xl mr-4" />
        <span className="text-white text-lg">Loading referral network...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-[#7FFFD4]/10 rounded-lg">
              <FaUsers className="text-[#7FFFD4] text-xl" />
            </div>
            <h3 className="text-lg font-semibold text-white">Total Network</h3>
          </div>
          <div className="text-3xl font-bold text-white mb-2">{getTotalReferrals()}</div>
          <p className="text-gray-400 text-sm">Referrals across all levels</p>
        </div>

        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-[#7FFFD4]/10 rounded-lg">
              <FaUser className="text-[#7FFFD4] text-xl" />
            </div>
            <h3 className="text-lg font-semibold text-white">Network Earnings</h3>
          </div>
          <div className="text-3xl font-bold text-white mb-2">${getTotalEarnings().toFixed(2)}</div>
          <p className="text-gray-400 text-sm">Total from referral network</p>
        </div>

        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-[#7FFFD4]/10 rounded-lg">
              <FaUsers className="text-[#7FFFD4] text-xl" />
            </div>
            <h3 className="text-lg font-semibold text-white">Active Levels</h3>
          </div>
          <div className="text-3xl font-bold text-white mb-2">{referralLevels.filter(l => l.count > 0).length}</div>
          <p className="text-gray-400 text-sm">Levels with referrals</p>
        </div>
      </div>

      {/* Referral Network */}
      <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-8 border border-gray-800/50">
        <div className="text-center">
          <h3 className="text-2xl font-bold text-white mb-2">Referral Network</h3>
          <p className="text-gray-400 text-sm mb-8">Your multi-level referral structure</p>

          {/* Level Statistics Grid */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 max-w-2xl mx-auto">
            {referralLevels.map((level) => (
              <div key={level.level} className="bg-[#0f1419] rounded-lg p-4 border border-gray-700/50 hover:border-[#7FFFD4]/30 transition-colors">
                <div className="text-[#7FFFD4] font-bold text-lg">{level.count}</div>
                <div className="text-gray-400 text-sm">Level {level.level}</div>
                <div className="text-orange-400 text-xs">{level.commission}%</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReferralTree;
