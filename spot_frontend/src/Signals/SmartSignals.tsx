import React, { useState } from 'react';
import { <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, FaCopy, FaExternalLinkAlt } from 'react-icons/fa';

interface SmartMoneyActivity {
  id: string;
  walletAddress: string;
  action: 'Buy' | 'Sell';
  token: string;
  amount: string;
  price: string;
  time: string;
  profit?: string;
  isProfit?: boolean;
}

interface KOLCall {
  id: string;
  kolName: string;
  avatar: string;
  token: string;
  action: string;
  time: string;
  followers: string;
}

const mockSmartMoneyData: SmartMoneyActivity[] = [
  {
    id: '1',
    walletAddress: 'King Phuong',
    action: 'Buy',
    token: '$GIGA',
    amount: '$9.00K',
    price: '$274.4K',
    time: '10mins',
    profit: '$1.3M',
    isProfit: true
  },
  {
    id: '2',
    walletAddress: 'BUDDY',
    action: 'Sell',
    token: '$GIGA',
    amount: '$9.00K',
    price: '$222.4K',
    time: '10mins',
    profit: '$1.3M',
    isProfit: true
  }
];

const mockKOLCalls: KOLCall[] = [
  {
    id: '1',
    kolName: 'CryptoKing',
    avatar: '👤',
    token: '$GIGA',
    action: 'Strong Buy',
    time: '5m ago',
    followers: '125K'
  },
  {
    id: '2',
    kolName: 'DeFiGuru',
    avatar: '👤',
    token: '$BUDDY',
    action: 'Hold',
    time: '12m ago',
    followers: '89K'
  }
];

const SmartSignals = () => {
  const [selectedTimeframe, setSelectedTimeframe] = useState('10mins');

  const QuickTradingButton = ({ percentage, isPositive }: { percentage: string; isPositive: boolean }) => (
    <button className={`px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200 ${
      isPositive 
        ? 'bg-green-500/20 text-green-400 hover:bg-green-500/30 border border-green-500/30' 
        : 'bg-red-500/20 text-red-400 hover:bg-red-500/30 border border-red-500/30'
    }`}>
      {isPositive ? '+' : ''}{percentage}%
    </button>
  );

  return (
    <div className="space-y-6">
      {/* Header with Subscribe Signal button */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">Smart Money Signals</h2>
          <p className="text-gray-400">Real-time tracking of smart money movements and KOL calls</p>
        </div>
        <button className="bg-[#7FFFD4] text-black px-6 py-2 rounded-full font-medium hover:bg-[#7FFFD4]/90 transition-colors flex items-center gap-2 w-fit">
          <span className="w-2 h-2 bg-green-500 rounded-full"></span>
          Subscribe Signal
        </button>
      </div>

      {/* Main Grid Layout */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        
        {/* Smart Money Activity - Left Column */}
        <div className="xl:col-span-2 space-y-6">
          {mockSmartMoneyData.map((activity, index) => (
            <div key={activity.id} className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
              {/* Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-[#7FFFD4]/10 rounded-full flex items-center justify-center">
                    <FaUser className="text-[#7FFFD4]" />
                  </div>
                  <div>
                    <h3 className="text-white font-medium">{activity.walletAddress}</h3>
                    <p className="text-gray-400 text-sm">Smart Wallet Buy</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-white font-medium">{activity.time}</p>
                  <p className="text-gray-400 text-sm">Alert Time</p>
                </div>
              </div>

              {/* AI Summary */}
              <div className="bg-[#1A1E24] rounded-lg p-4 mb-4">
                <h4 className="text-white font-medium mb-2">AI Summary</h4>
                <p className="text-gray-300 text-sm">
                  {activity.walletAddress} is a smart wallet that shows its narrative from the last trading setups. 
                  {activity.action === 'Buy' ? ' Recently bought' : ' Recently sold'} {activity.token} with strong conviction.
                </p>
              </div>

              {/* Smart Money Activity Table */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-white font-medium">Smart Money Activity - {selectedTimeframe}</h4>
                  <div className="flex items-center gap-2">
                    <span className="text-gray-400 text-sm">Smart Money Hold:</span>
                    <span className="text-[#7FFFD4] font-medium">2</span>
                    <span className="text-gray-400 text-sm">Sold All:</span>
                    <span className="text-red-400 font-medium">1</span>
                  </div>
                </div>

                {/* Progress bar */}
                <div className="w-full bg-gray-700 rounded-full h-2 mb-4">
                  <div className="bg-gradient-to-r from-green-500 to-red-500 h-2 rounded-full" style={{ width: '75%' }}></div>
                </div>

                {/* Activity entries */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                      <span className="text-green-400">{activity.action} (${activity.action})</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-gray-300">+{activity.amount} {activity.token}</span>
                      <span className="text-gray-300">{activity.price}</span>
                      <span className="text-gray-400">{activity.time} ago</span>
                    </div>
                  </div>
                </div>

                <button className="text-[#7FFFD4] text-sm mt-2 hover:underline">View All</button>
              </div>

              {/* KOL Calls */}
              <div className="mb-4">
                <h4 className="text-white font-medium mb-3">KOL Calls</h4>
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="text-center">
                      <div className="w-12 h-12 bg-[#7FFFD4]/10 rounded-full flex items-center justify-center mb-2 mx-auto">
                        <span className="text-[#7FFFD4] text-sm">{i}K</span>
                      </div>
                      <p className="text-gray-400 text-xs">{i * 2}K</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Quick Trading */}
              <div>
                <h4 className="text-white font-medium mb-3">Quick Trading</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <p className="text-gray-400 text-sm mb-2">Buy/Sell</p>
                    <div className="grid grid-cols-2 gap-2">
                      <QuickTradingButton percentage="0.1" isPositive={true} />
                      <QuickTradingButton percentage="0.2" isPositive={true} />
                      <QuickTradingButton percentage="0.5" isPositive={true} />
                      <QuickTradingButton percentage="1" isPositive={true} />
                    </div>
                  </div>
                  <div>
                    <p className="text-gray-400 text-sm mb-2">Sell</p>
                    <div className="grid grid-cols-2 gap-2">
                      <QuickTradingButton percentage="10" isPositive={false} />
                      <QuickTradingButton percentage="25" isPositive={false} />
                      <QuickTradingButton percentage="50" isPositive={false} />
                      <QuickTradingButton percentage="100" isPositive={false} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Right Sidebar */}
        <div className="space-y-6">
          {/* Today's Gains */}
          <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
            <h3 className="text-white font-medium mb-4">Today's Gains</h3>
            <div className="space-y-3">
              {['Crypto1', 'Crypto2', 'Crypto3'].map((crypto, index) => (
                <div key={crypto} className="flex items-center justify-between">
                  <span className="text-gray-300">{crypto}</span>
                  <span className="text-green-400">+{(index + 1) * 5}%</span>
                </div>
              ))}
            </div>
          </div>

          {/* KOL Calls Widget */}
          <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
            <h3 className="text-white font-medium mb-4">KOL Calls</h3>
            <div className="space-y-4">
              {mockKOLCalls.map((call) => (
                <div key={call.id} className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-[#7FFFD4]/10 rounded-full flex items-center justify-center">
                    <span className="text-xs">{call.avatar}</span>
                  </div>
                  <div className="flex-1">
                    <p className="text-white text-sm font-medium">{call.kolName}</p>
                    <p className="text-gray-400 text-xs">{call.followers} followers</p>
                  </div>
                  <div className="text-right">
                    <p className="text-[#7FFFD4] text-sm">{call.action}</p>
                    <p className="text-gray-400 text-xs">{call.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SmartSignals;
