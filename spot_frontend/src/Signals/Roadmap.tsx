import React from 'react';
import HorizontalTimeline, { TimelineItem } from '../components/HorizontalTimeline';

const roadmapData: TimelineItem[] = [
  {
    id: '1',
    period: "Q1 '24",
    title: 'Foundation',
    status: 'completed',
    avatar: '🚀',
    timeIndicator: '02:05',
    description: 'Smart Wallet Tracking, Portfolio Analytics, Basic Trading'
  },
  {
    id: '2',
    period: "Q2 '24",
    title: 'Growth',
    status: 'completed',
    avatar: '📈',
    timeIndicator: '05:42',
    description: 'Advanced Signals, KOL Tracking, Referral System'
  },
  {
    id: '3',
    period: "Q3 '24",
    title: 'Innovation',
    status: 'in-progress',
    avatar: '⚡',
    timeIndicator: '08:05',
    description: 'AI Trading Bots, Cross-chain Support, Mobile App'
  },
  {
    id: '4',
    period: "Q4 '24",
    title: 'DeFi',
    status: 'upcoming',
    avatar: '💎',
    timeIndicator: '11:05',
    description: 'DeFi Integration, Yield Farming, Governance Token'
  },
  {
    id: '5',
    period: "Q1 '25",
    title: 'Expansion',
    status: 'upcoming',
    avatar: '🌐',
    timeIndicator: '14:20',
    description: 'Layer 2 Support, NFT Trading, Social Features'
  },
  {
    id: '6',
    period: "Q2 '25",
    title: 'Enterprise',
    status: 'upcoming',
    avatar: '🏢',
    timeIndicator: '17:45',
    description: 'Institutional Tools, API Access, White Label'
  },
  {
    id: '7',
    period: "Q3 '25",
    title: 'Global',
    status: 'upcoming',
    avatar: '🌍',
    timeIndicator: '20:30',
    description: 'Worldwide expansion and partnerships'
  },
  {
    id: '8',
    period: "Q4 '25",
    title: 'Future',
    status: 'upcoming',
    avatar: '🔮',
    timeIndicator: '23:50',
    description: 'Next-generation features and innovations'
  }
];

const Roadmap = () => {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-white mb-4">RedFyn Development Roadmap</h2>
        <p className="text-gray-400 text-lg">Track our progress and upcoming features</p>
      </div>

      {/* Horizontal Timeline */}
      <div className="w-full">
        <HorizontalTimeline
          items={roadmapData}
          className="py-4"
        />
      </div>

      {/* Status Legend */}
      <div className="max-w-4xl mx-auto mt-12">
        <h3 className="text-white font-semibold mb-4 text-center">Status Legend</h3>
        <div className="flex justify-center gap-8">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-green-500 rounded-full shadow-lg shadow-green-500/30"></div>
            <span className="text-gray-300 text-sm">Completed</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-yellow-500 rounded-full shadow-lg shadow-yellow-500/30"></div>
            <span className="text-gray-300 text-sm">In Progress</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-gray-500 rounded-full shadow-lg shadow-gray-500/20"></div>
            <span className="text-gray-300 text-sm">Upcoming</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Roadmap;
