import React, { useState } from "react";
import Roadmap from "./Roadmap";
import SmartSignals from "./SmartSignals";

interface TabConfig {
  id: "Roadmap" | "SmartSignals";
  label: string;
  icon: React.ReactNode;
}

const tabs: TabConfig[] = [
  {
    id: "Roadmap",
    label: "Roadmap",
    icon: (
      <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>
    ),
  },
  {
    id: "SmartSignals",
    label: "Smart Signals",
    icon: (
      <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
    ),
  },
];

const SignalsTabs = () => {
  const [activeTab, setActiveTab] = useState<"Roadmap" | "SmartSignals">("Roadmap");

  const renderContent = () => {
    switch (activeTab) {
      case "Roadmap":
        return <Roadmap />;
      case "SmartSignals":
        return <SmartSignals />;
      default:
        return null;
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Modern pill-style tabs */}
      <div className="flex items-center mb-6 overflow-x-auto">
        <div className="relative bg-[#181C20] rounded-full p-1 flex min-w-max">
          {/* Animated background pill */}
          <div
            className="absolute top-1 bottom-1 bg-gradient-to-r from-[#7FFFD4]/20 to-[#7FFFD4]/10 rounded-full transition-all duration-300 ease-out"
            style={{
              left: activeTab === "Roadmap" ? "4px" : "50%",
              width: "calc(50% - 4px)",
            }}
          />
          
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`relative z-10 flex items-center gap-2 px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                activeTab === tab.id
                  ? "text-white"
                  : "text-gray-400 hover:text-gray-200"
              }`}
            >
              <span className={`transition-all duration-300 ${
                activeTab === tab.id ? "text-[#7FFFD4]" : ""
              }`}>
                {tab.icon}
              </span>
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      <div className="flex-1 h-full overflow-auto">{renderContent()}</div>
    </div>
  );
};

export default SignalsTabs;
