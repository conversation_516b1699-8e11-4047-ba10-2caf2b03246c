# HorizontalTimeline Component

A reusable React component that creates a horizontal timeline with milestones, perfect for displaying roadmaps, project timelines, or progress tracking.

## Features

- **Horizontal Layout**: Timeline flows from left to right across the screen
- **Responsive Design**: Works on both desktop and mobile devices with horizontal scrolling
- **Status Indicators**: Visual states for completed, in-progress, and upcoming milestones
- **Customizable**: Supports avatars, time indicators, and descriptions
- **RedFyn Design System**: Consistent with existing styling patterns
- **Interactive**: Hover effects and smooth transitions
- **Accessible**: Proper contrast and keyboard navigation support

## Props

### TimelineItem Interface

```typescript
interface TimelineItem {
  id: string;                    // Unique identifier
  period: string;                // e.g., "Q1 '24", "Jan 2024"
  title: string;                 // Milestone title
  status: 'completed' | 'in-progress' | 'upcoming';
  avatar?: string;               // URL, emoji, or icon
  timeIndicator?: string;        // e.g., "02:05", "3 months"
  description?: string;          // Additional details
}
```

### Component Props

```typescript
interface HorizontalTimelineProps {
  items: TimelineItem[];         // Array of timeline items
  className?: string;            // Additional CSS classes
}
```

## Usage Examples

### Basic Usage

```tsx
import HorizontalTimeline, { TimelineItem } from './components/HorizontalTimeline';

const timelineData: TimelineItem[] = [
  {
    id: '1',
    period: "Q1 '24",
    title: 'Foundation',
    status: 'completed',
    avatar: '🚀',
    timeIndicator: '02:05'
  },
  {
    id: '2',
    period: "Q2 '24",
    title: 'Growth',
    status: 'in-progress',
    avatar: '📈',
    timeIndicator: '05:42'
  }
];

function MyComponent() {
  return (
    <HorizontalTimeline 
      items={timelineData}
      className="my-8"
    />
  );
}
```

### With Full Width Layout (Smart Wallet Style)

```tsx
function FullWidthTimeline() {
  return (
    <div className="min-h-screen flex flex-col bg-[#141416]">
      <div className="w-full px-4 sm:px-6 lg:px-8 h-full">
        <div className="py-6 h-full w-full">
          <h1 className="text-2xl font-bold text-white mb-6">Project Timeline</h1>
          <HorizontalTimeline 
            items={timelineData}
            className="w-full"
          />
        </div>
      </div>
    </div>
  );
}
```

### Custom Avatar Types

```tsx
const timelineWithDifferentAvatars: TimelineItem[] = [
  {
    id: '1',
    period: "Phase 1",
    title: 'Planning',
    status: 'completed',
    avatar: '📋', // Emoji
  },
  {
    id: '2',
    period: "Phase 2",
    title: 'Development',
    status: 'in-progress',
    avatar: 'https://example.com/avatar.jpg', // Image URL
  },
  {
    id: '3',
    period: "Phase 3",
    title: 'Testing',
    status: 'upcoming',
    // No avatar - will show default dot
  }
];
```

## Styling

### Color Scheme

- **Background**: `#141416` (RedFyn dark theme)
- **Completed**: Green (`#10B981`) with glow effect
- **In Progress**: Yellow (`#F59E0B`) with pulse animation
- **Upcoming**: Gray (`#6B7280`) with subtle styling
- **Accent**: `#7FFFD4` (RedFyn brand color)

### Responsive Behavior

- **Desktop**: Full horizontal layout with proper spacing
- **Mobile**: Horizontal scroll with scroll indicators
- **Tablet**: Optimized spacing and touch-friendly interactions

## Integration with Existing Pages

### In Signals Page (Current Implementation)

```tsx
// spot_frontend/src/Signals/Roadmap.tsx
import HorizontalTimeline, { TimelineItem } from '../components/HorizontalTimeline';

const Roadmap = () => {
  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-white mb-4">RedFyn Development Roadmap</h2>
        <p className="text-gray-400 text-lg">Track our progress and upcoming features</p>
      </div>
      
      <HorizontalTimeline 
        items={roadmapData}
        className="py-4"
      />
    </div>
  );
};
```

### As Standalone Page

```tsx
// Create a dedicated timeline page
import HorizontalTimeline from '../components/HorizontalTimeline';

const TimelinePage = () => {
  return (
    <div className="min-h-screen bg-[#141416]">
      <Navbar />
      <main className="flex-1 overflow-hidden">
        <div className="w-full px-4 sm:px-6 lg:px-8 h-full">
          <div className="py-6 h-full w-full">
            <HorizontalTimeline items={timelineData} />
          </div>
        </div>
      </main>
    </div>
  );
};
```

## Customization

### Custom Status Colors

The component uses a predefined color scheme, but you can extend it by modifying the `getStatusColor` and `getStatusGlow` functions within the component.

### Custom Animations

The component includes hover effects and status-specific animations:
- Completed items show a checkmark overlay
- In-progress items have a pulsing ring
- All items scale on hover

### Mobile Optimization

- Horizontal scrolling with hidden scrollbars
- Touch-friendly milestone sizes
- Scroll indicators for better UX
- Responsive text sizing

## Browser Support

- Modern browsers with CSS Grid and Flexbox support
- Mobile Safari and Chrome
- Desktop Chrome, Firefox, Safari, Edge

## Performance Considerations

- Efficient rendering with React keys
- CSS transforms for smooth animations
- Minimal DOM manipulation
- Optimized for large datasets (100+ items)

## Accessibility

- Semantic HTML structure
- Proper color contrast ratios
- Keyboard navigation support
- Screen reader friendly labels
