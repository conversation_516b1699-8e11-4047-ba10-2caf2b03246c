import React from 'react';
import HorizontalTimeline, { TimelineItem } from './HorizontalTimeline';

const TimelineExample: React.FC = () => {
  // Example data matching the screenshot style
  const timelineData: TimelineItem[] = [
    {
      id: '1',
      period: "Q1 '24",
      title: "Foundation",
      status: 'completed',
      avatar: '🚀',
      timeIndicator: '02:05',
      description: 'Core platform launch'
    },
    {
      id: '2',
      period: "Q2 '24", 
      title: "Growth",
      status: 'completed',
      avatar: '📈',
      timeIndicator: '05:42',
      description: 'User acquisition'
    },
    {
      id: '3',
      period: "Q3 '24",
      title: "Features",
      status: 'completed',
      avatar: '⚡',
      timeIndicator: '08:05',
      description: 'Advanced tools'
    },
    {
      id: '4',
      period: "Q4 '24",
      title: "AI Integration",
      status: 'in-progress',
      avatar: '🤖',
      timeIndicator: '11:05',
      description: 'Smart algorithms'
    },
    {
      id: '5',
      period: "Q1 '25",
      title: "Mobile",
      status: 'upcoming',
      avatar: '📱',
      timeIndicator: '14:20',
      description: 'Mobile app release'
    },
    {
      id: '6',
      period: "Q2 '25",
      title: "Enterprise",
      status: 'upcoming',
      avatar: '🏢',
      timeIndicator: '17:45',
      description: 'B2B solutions'
    },
    {
      id: '7',
      period: "Q3 '25",
      title: "Global",
      status: 'upcoming',
      avatar: '🌍',
      timeIndicator: '20:30',
      description: 'Worldwide expansion'
    },
    {
      id: '8',
      period: "Q4 '25",
      title: "Innovation",
      status: 'upcoming',
      avatar: '💡',
      timeIndicator: '23:50',
      description: 'Next-gen features'
    }
  ];

  return (
    <div className="min-h-screen bg-[#141416]">
      {/* Header */}
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-4">Development Roadmap</h1>
          <p className="text-gray-400 text-lg">Track our progress and upcoming milestones</p>
        </div>
      </div>

      {/* Timeline */}
      <div className="w-full">
        <HorizontalTimeline 
          items={timelineData}
          className="py-8"
        />
      </div>

      {/* Legend */}
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-4xl mx-auto">
          <h3 className="text-white font-semibold mb-4">Status Legend</h3>
          <div className="flex flex-wrap gap-6">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-green-500 rounded-full"></div>
              <span className="text-gray-300 text-sm">Completed</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-yellow-500 rounded-full"></div>
              <span className="text-gray-300 text-sm">In Progress</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-gray-500 rounded-full"></div>
              <span className="text-gray-300 text-sm">Upcoming</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TimelineExample;
